
<?php $__env->startSection('page_title', 'Gérer les notes'); ?>
<?php $__env->startSection('content'); ?>

    <div class="card">
        <div class="card-header header-elements-inline">
            <h6 class="card-title font-weight-bold">Remplissez le formulaire pour gérer les notes</h6>
            <?php echo Qs::getPanelOptions(); ?>

        </div>

        <div class="card-body">
            <?php echo $__env->make('pages.support_team.marks.selector', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>

    <div class="card">

        <div class="card-header">
            <div class="row">
                <div class="col-md-4"><h6 class="card-title"><strong>Matière : </strong> <?php echo e($m->subject->name); ?></h6></div>
                <div class="col-md-4"><h6 class="card-title"><strong>Classe : </strong> <?php echo e($m->my_class->name.' '.$m->section->name); ?></h6></div>
                <div class="col-md-4"><h6 class="card-title"><strong>Examen : </strong> <?php echo e($m->exam->name.' - '.$m->year); ?></h6></div>
            </div>
        </div>

        <div class="card-body">
            <?php echo $__env->make('pages.support_team.marks.edit', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            
        </div>
    </div>

    

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/marks/manage.blade.php ENDPATH**/ ?>