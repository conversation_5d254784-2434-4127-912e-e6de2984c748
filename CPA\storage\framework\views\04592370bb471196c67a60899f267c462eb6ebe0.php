
<table class="table table-bordered table-responsive text-center">
    <thead>
        <tr>
            <th rowspan="2">N°</th>
            <th rowspan="2">MATIÈRES</th>
            <th rowspan="2">CA1<br>(20)</th>
            <th rowspan="2">CA2<br>(20)</th>
            <th rowspan="2">EXAMENS<br>(20)</th>
            <th rowspan="2"><PERSON>ye<PERSON> (/20)<br></th>

            

            <th rowspan="2">coefficient</th>
            <th rowspan="2">total avec Coef</th>
            <th rowspan="2">REMARQUES</th>
        </tr>
    </thead>

    <tbody>
        <?php $__currentLoopData = $subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($loop->iteration); ?></td>
                <td><?php echo e($sub->name); ?></td>
                <?php $__currentLoopData = $marks->where('subject_id', $sub->id)->where('exam_id', $ex->id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <td><?php echo e($mk->t1 ?: '-'); ?></td>
                    <td><?php echo e($mk->t2 ?: '-'); ?></td>
                    <td><?php echo e($mk->exm ?: '-'); ?></td>
                    <td>
                        <?php
                            // Récupérer les valeurs de t1, t2, et exm
                            $t1 = $mk->t1 ?: 0; // Si t1 est null, on le considère comme 0
                            $t2 = $mk->t2 ?: 0; // Si t2 est null, on le considère comme 0
                            $exm = $mk->exm ?: 0; // Si exm est null, on le considère comme 0

                            // Calcul de la moyenne sans coefficient (en ne divisant que si on a des valeurs)
                            $values = [$t1, $t2, $exm];
                            $sum = array_sum($values); // Additionner toutes les notes
                            $count = count(array_filter($values, function($value) { return $value > 0; })); // Compter combien de valeurs sont supérieures à 0

                            // Si count est supérieur à 0 (au moins une note), on calcule la moyenne
                            $moyen_sans_coef = $count > 0 ? $sum / $count : 0;
                        ?>

                        <?php echo e(number_format($moyen_sans_coef, 2)); ?> <!-- Afficher la moyenne, formatée avec 2 décimales -->
                    </td>

                    
                    

                    

                    <td class="coef-<?php echo e($ex->id); ?>"><?php echo e($sub->coef); ?></td>
                    <td class="notetotalaveccoef-<?php echo e($ex->id); ?>">
                        

                        <?php
                            $multipliedValue = 0;
                            if ($ex->term === 1) {
                                $multipliedValue = $mk->tex1;
                            } elseif ($ex->term === 2) {
                                $multipliedValue = $mk->tex2;
                            } elseif ($ex->term === 3) {
                                $multipliedValue = $mk->tex3;
                            }
                        ?>
                        <?php echo e(number_format($multipliedValue, 1)); ?>

                    </td>
                    <td>
                        <?php
                            // Récupérer les valeurs de t1, t2, et exm
                            $t1 = $mk->t1 ?: 0;
                            $t2 = $mk->t2 ?: 0;
                            $exm = $mk->exm ?: 0;

                            // Calcul de la moyenne sans coefficient
                            $values = [$t1, $t2, $exm];
                            $sum = array_sum($values);
                            $count = count(array_filter($values, function($value) { return $value > 0; }));
                            $moyen_sans_coef = $count > 0 ? $sum / $count : 0;
                            
                            // Générer le commentaire basé sur la moyenne
                            $comment = \App\Helpers\MarkComment::getComment($moyen_sans_coef);
                            $commentColor = \App\Helpers\MarkComment::getCommentColor($moyen_sans_coef);
                        ?>
                        
                        <span class="<?php echo e($commentColor); ?>"><?php echo e($comment ?: ($mk->comment ?: '-')); ?></span>
                    </td>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <script>
                    $(document).ready(function() {
                        var totalpoint = 0;
                        var total_coef = 0;

                        // Parcourir chaque ligne de matière
                        $(".notetotalaveccoef-<?php echo e($ex->id); ?>").each(function(index) {
                            var value = parseFloat($(this).text());
                            
                            // Vérifier si la valeur est différente de zéro (matière avec au moins une note)
                            if (!isNaN(value) && value > 0) {
                                totalpoint += value;
                                
                                // Récupérer le coefficient correspondant à cette matière
                                var coef = parseFloat($(".coef-<?php echo e($ex->id); ?>").eq(index).text());
                                if (!isNaN(coef)) {
                                    total_coef += coef;
                                }
                            }
                        });

                        // Calculer la moyenne seulement si le total des coefficients est supérieur à zéro
                        var moyenne = (total_coef > 0) ? (totalpoint / total_coef) : 0;

                        var soratra = "TOTALE DES POINTS OBTENUS: " + totalpoint.toFixed(2);
                        var soratra2 = "Moyenne " + moyenne.toFixed(2);

                        // Display the concatenated total value wherever you want
                        $(".P_totalpoi-<?php echo e($ex->id); ?>").text(soratra);
                        $(".P_moyenne-<?php echo e($ex->id); ?>").text(soratra2);
                    });
                </script>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <?php
                    use App\Models\StudentRecord;
                    use App\Models\ExamRecord;


                $total_eleve = StudentRecord::where('my_class_id', $my_class->id)->count();
                $position = Mk::getSuffix($exr->where('student_id', $sr->user->id)->first()->pos) ?: '-';

                //dd($exr->where('student_id', $sr->user->id));




                $rang = ExamRecord::where('my_class_id', $my_class->id)->where('student_id', $sr->user->id)->where('exam_id',$ex->id)->first();



                $positionEnFrancais = '';

                // Tableau de correspondance pour les numéros ordinaux
                $positionsEnFrancais = [
                    '1st' => '1er',
                    '2nd' => '2e',
                    '3rd' => '3e',
                    '4th' => '4e',
                    '5th' => '5e',
                    '6th' => '6e',
                    '7th' => '7e',
                    '8th' => '8e',
                    '9th' => '9e',
                    '10th' => '10e',
                    '11th' => '11e',
                    '12th' => '12e',
                    '13th' => '13e',
                    '14th' => '14e',
                    '15th' => '15e',
                    '16th' => '16e',
                    '17th' => '17e',
                    '18th' => '18e',
                    '19th' => '19e',
                    '20th' => '20e',
                ];

                // Vérifiez si la position est dans le tableau de correspondance
                if (array_key_exists($position, $positionsEnFrancais)) {
                    $positionEnFrancais = $positionsEnFrancais[$position];
                } else {
                    $positionEnFrancais = str_replace(['st', 'nd', 'rd', 'th'], ['er', 'éme', 'éme', 'éme'], $position);
                }
            ?>

            <td colspan="4" class="P_totalpoi-<?php echo e($ex->id); ?>"><strong>TOTAL DES POINTS OBTENUS :</strong></td>
            <td colspan="3" class="P_moyenne-<?php echo e($ex->id); ?>"><strong>MOYENNE FINALE :</strong> <?php echo e($exr->ave); ?></td>
            <td colspan="2"><strong>RANG: <?php echo $positionEnFrancais; ?> / <?php echo $total_eleve; ?></strong></td>
        </tr>
    </tbody>
</table>



<!-- Commentaire général de l'enseignant -->
<div class="card mt-3">
    <div class="card-header bg-light">
        <h5 class="card-title">Commentaire de l'enseignant</h5>
    </div>
    <div class="card-body">
        <div id="commentaire_general_<?php echo e($ex->id); ?>" class="p-3 bg-light rounded">
            <!-- Le commentaire sera inséré ici par JavaScript -->
        </div>
    </div>
</div>

<?php if($ex->term == 3): ?>
<!-- Affichage de la moyenne générale annuelle -->
<div class="card mt-3">
    <div class="card-header bg-light">
        <h5 class="card-title">Moyennes annuelles</h5>
    </div>
    <div class="card-body">
        <?php
            // Récupérer tous les examens créés pour cette année
            $all_exams = \App\Models\Exam::where('year', $year)->orderBy('term')->get();
        ?>

        <div class="row">
            <?php $__currentLoopData = $all_exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="font-weight-bold">Moyenne <?php echo e($exam->name); ?> :</label>
                        <div class="form-control-plaintext exam-average" data-exam-id="<?php echo e($exam->id); ?>" id="moyenne_exam_<?php echo e($exam->id); ?>">
                            Calcul en cours...
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="row mt-3">
            <div class="col-12 text-center">
                <h4 class="font-weight-bold">Moyenne générale annuelle : <span id="moyenne_annuelle_finale">Calcul en cours...</span></h4>
            </div>
        </div>
    </div>
</div>


<?php endif; ?>

<script>
    $(document).ready(function() {
        // Attendre que la moyenne soit calculée
        setTimeout(function() {
            var totalpoint = 0;
            var total_coef = 0;
            
            // Parcourir chaque ligne de matière
            $(".notetotalaveccoef-<?php echo e($ex->id); ?>").each(function(index) {
                var value = parseFloat($(this).text());
                
                // Vérifier si la valeur est différente de zéro (matière avec au moins une note)
                if (!isNaN(value) && value > 0) {
                    totalpoint += value;
                    
                    // Récupérer le coefficient correspondant à cette matière
                    var coef = parseFloat($(".coef-<?php echo e($ex->id); ?>").eq(index).text());
                    if (!isNaN(coef)) {
                        total_coef += coef;
                    }
                }
            });
            
            var moyenne = total_coef > 0 ? totalpoint / total_coef : 0;
            var moyenne_sur_20 = moyenne;
            
            // Stocker la moyenne pour d'autres calculs
            window.moyenne_generale = moyenne_sur_20;
            
            var commentaire = "";
            var commentaireClass = "";
            
            // Déterminer le commentaire en fonction de la moyenne
            if (moyenne_sur_20 >= 0 && moyenne_sur_20 < 5) {
                commentaire = "Moyenne très faible. Il faut persévérer, chaque progrès compte.";
                commentaireClass = "text-danger font-weight-bold";
            } else if (moyenne_sur_20 >= 5 && moyenne_sur_20 < 8) {
                commentaire = "Résultats insuffisants, mais des efforts peuvent relancer la dynamique.";
                commentaireClass = "text-warning font-weight-bold";
            } else if (moyenne_sur_20 >= 8 && moyenne_sur_20 < 10) {
                commentaire = "Moyenne fragile. Du potentiel à développer avec plus de régularité.";
                commentaireClass = "text-warning font-weight-bold";
            } else if (moyenne_sur_20 >= 10 && moyenne_sur_20 < 12) {
                commentaire = "Moyenne juste. Des bases sont posées, il faut les renforcer.";
                commentaireClass = "text-primary font-weight-bold";
            } else if (moyenne_sur_20 >= 12 && moyenne_sur_20 < 14) {
                commentaire = "Moyenne correcte. Il faut continuer à s'investir pour consolider les acquis.";
                commentaireClass = "text-primary font-weight-bold";
            } else if (moyenne_sur_20 >= 14 && moyenne_sur_20 < 16) {
                commentaire = "Bon travail. Un bel investissement à maintenir.";
                commentaireClass = "text-success font-weight-bold";
            } else if (moyenne_sur_20 >= 16 && moyenne_sur_20 < 18) {
                commentaire = "Très bon niveau. L'élève est sérieux et régulier.";
                commentaireClass = "text-success font-weight-bold";
            } else if (moyenne_sur_20 >= 18 && moyenne_sur_20 <= 20) {
                commentaire = "Excellent parcours. Un exemple à suivre, bravo !";
                commentaireClass = "text-purple font-weight-bold";
            }
            
            // Afficher le commentaire avec la classe de couleur appropriée
            $("#commentaire_general_<?php echo e($ex->id); ?>").html('<span class="' + commentaireClass + '">' + commentaire + '</span>');
            
            // Mettre à jour la moyenne de l'examen actuel
            $("#moyenne_exam_<?php echo e($ex->id); ?>").text(moyenne_sur_20.toFixed(2) + "/20");

            // Si c'est le 3ème trimestre, calculer la moyenne annuelle
            <?php if($ex->term == 3): ?>
                // Fonction unifiée pour valider une moyenne
                function validateAverage(average, examName) {
                    if (average > 20) {
                        console.log("ATTENTION: Moyenne " + examName + " (" + average.toFixed(2) + ") dépasse 20, limitée à 20");
                        return 20;
                    }
                    return average;
                }

                // Fonction unifiée pour récupérer la moyenne d'un examen
                function getExamAverage(examId, examName, isCurrentExam) {
                    if (isCurrentExam) {
                        return validateAverage(moyenne_sur_20, examName + " (actuel)");
                    }

                    // Essayer de récupérer depuis P_moyenne
                    var examElement = $(".P_moyenne-" + examId);
                    if (examElement.length > 0) {
                        var examText = examElement.text();
                        console.log("Texte P_moyenne-" + examId + ": '" + examText + "'");

                        // Format "Moyenne 12.23"
                        var match = examText.match(/Moyenne\s+(\d+\.?\d*)/);
                        if (match && match[1]) {
                            return validateAverage(parseFloat(match[1]), examName + " (format Moyenne)");
                        }

                        // Format "MOYENNE FINALE : 12.23"
                        var match2 = examText.match(/MOYENNE FINALE\s*:\s*(\d+\.?\d*)/);
                        if (match2 && match2[1]) {
                            return validateAverage(parseFloat(match2[1]), examName + " (format MOYENNE FINALE)");
                        }
                    }

                    return null; // Aucune moyenne trouvée
                }

                // Fonction pour calculer la moyenne annuelle à partir des éléments P_moyenne
                function calculateAnnualAverage() {
                    var examAverages = [];
                    var totalAverage = 0;
                    var validExams = 0;

                    console.log("=== CALCUL DES MOYENNES ANNUELLES (SHOW) ===");
                    console.log("Moyenne actuelle calculée: " + moyenne_sur_20.toFixed(2));

                    // Récupérer toutes les moyennes des examens
                    <?php $__currentLoopData = $all_exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        var examAverage = getExamAverage(<?php echo e($exam->id); ?>, "<?php echo e($exam->name); ?>", <?php echo e($exam->id == $ex->id ? 'true' : 'false'); ?>);

                        if (examAverage !== null && examAverage > 0) {
                            examAverages.push(examAverage);
                            totalAverage += examAverage;
                            validExams++;
                            console.log("Examen <?php echo e($exam->name); ?>: " + examAverage.toFixed(2));
                            $("#moyenne_exam_<?php echo e($exam->id); ?>").text(examAverage.toFixed(2) + "/20");
                        } else {
                                // Fallback: essayer de récupérer depuis la base de données
                                <?php
                                    $exam_record = \App\Models\ExamRecord::where([
                                        'student_id' => $sr->user->id,
                                        'exam_id' => $exam->id,
                                        'year' => $year
                                    ])->first();
                                    $exam_average = $exam_record ? ($exam_record->ave ?? 0) : 0;
                                ?>
                                console.log("Fallback DB pour <?php echo e($exam->name); ?>: <?php echo e($exam_average); ?>");
                                <?php if($exam_average > 0): ?>
                                    var fallbackAverage = validateAverage(<?php echo e(number_format($exam_average, 2)); ?>, "<?php echo e($exam->name); ?> (fallback DB)");
                                    examAverages.push(fallbackAverage);
                                    totalAverage += fallbackAverage;
                                    validExams++;
                                    console.log("Examen <?php echo e($exam->name); ?> (fallback DB): " + fallbackAverage.toFixed(2));
                                    $("#moyenne_exam_<?php echo e($exam->id); ?>").text(fallbackAverage.toFixed(2) + "/20");
                                <?php else: ?>
                                    console.log("Aucune moyenne trouvée en DB pour <?php echo e($exam->name); ?>");
                                    // Essayer de calculer la moyenne manuellement si possible
                                    <?php
                                        // Calculer la moyenne manuellement à partir des marks
                                        $marks = \App\Models\Mark::where([
                                            'student_id' => $sr->user->id,
                                            'exam_id' => $exam->id,
                                            'my_class_id' => $my_class->id,
                                            'year' => $year
                                        ])->get();

                                        $totalPoints = 0;
                                        $totalCoef = 0;

                                        foreach($marks as $mark) {
                                            $subject = \App\Models\Subject::find($mark->subject_id);
                                            if($subject) {
                                                // Calculer la moyenne de la matière (t1 + t2 + exm) / nombre de notes
                                                $t1 = $mark->t1 ?: 0;
                                                $t2 = $mark->t2 ?: 0;
                                                $exm = $mark->exm ?: 0;

                                                $values = [$t1, $t2, $exm];
                                                $sum = array_sum($values);
                                                $count = count(array_filter($values, function($value) { return $value > 0; }));
                                                $moyen_sans_coef = $count > 0 ? $sum / $count : 0;

                                                // Utiliser cette moyenne avec le coefficient
                                                if($moyen_sans_coef > 0) {
                                                    $totalPoints += ($moyen_sans_coef * $subject->coef);
                                                    $totalCoef += $subject->coef;
                                                }
                                            }
                                        }

                                        $calculated_average = $totalCoef > 0 ? $totalPoints / $totalCoef : 0;

                                        // Validation: s'assurer que la moyenne ne dépasse pas 20
                                        if($calculated_average > 20) {
                                            $calculated_average = 20;
                                        }
                                    ?>
                                    <?php if($calculated_average > 0): ?>
                                        var calculatedAverage = validateAverage(<?php echo e(number_format($calculated_average, 2)); ?>, "<?php echo e($exam->name); ?> (calculé manuellement)");
                                        examAverages.push(calculatedAverage);
                                        totalAverage += calculatedAverage;
                                        validExams++;
                                        console.log("Examen <?php echo e($exam->name); ?> (calculé manuellement): " + calculatedAverage.toFixed(2));
                                        $("#moyenne_exam_<?php echo e($exam->id); ?>").text(calculatedAverage.toFixed(2) + "/20");
                                    <?php else: ?>
                                        console.log("Impossible de calculer la moyenne pour <?php echo e($exam->name); ?>");
                                        $("#moyenne_exam_<?php echo e($exam->id); ?>").text("0.00/20");
                                    <?php endif; ?>
                                <?php endif; ?>
                        }
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    console.log("=== RÉSUMÉ DES MOYENNES (SHOW) ===");
                    console.log("Examens valides: " + validExams);
                    console.log("Total des moyennes: " + totalAverage.toFixed(2));
                    console.log("Moyennes individuelles: " + examAverages.map(avg => avg.toFixed(2)).join(", "));
                    console.log("Calcul: (" + examAverages.map(avg => avg.toFixed(2)).join(" + ") + ") / " + validExams + " = " + (totalAverage / validExams).toFixed(2));

                    // Calculer et afficher la moyenne annuelle
                    if (validExams > 0) {
                        var annualAverage = validateAverage(totalAverage / validExams, "Moyenne annuelle");

                        console.log("Moyenne annuelle finale (SHOW): " + annualAverage.toFixed(2));
                        $("#moyenne_annuelle_finale").text(annualAverage.toFixed(2) + "/20");

                        // Stocker la moyenne annuelle pour utilisation ultérieure et comparaison
                        window.moyenne_annuelle_finale_show = annualAverage;
                        window.moyenne_annuelle_finale = annualAverage;
                    } else {
                        console.log("Aucun examen valide, affichage 0.00");
                        $("#moyenne_annuelle_finale").text("0.00/20");
                        window.moyenne_annuelle_finale = 0;
                    }
                }

                // Fonction pour s'assurer que tous les examens affichent leurs moyennes
                function ensureAllExamAveragesDisplayed() {
                    <?php $__currentLoopData = $all_exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($exam->id == $ex->id): ?>
                            // L'examen actuel est déjà mis à jour
                            if ($("#moyenne_exam_<?php echo e($exam->id); ?>").text() === "Calcul en cours...") {
                                $("#moyenne_exam_<?php echo e($exam->id); ?>").text(moyenne_sur_20.toFixed(2) + "/20");
                            }
                        <?php else: ?>
                            // Vérifier si l'examen a une moyenne affichée
                            if ($("#moyenne_exam_<?php echo e($exam->id); ?>").text() === "Calcul en cours...") {
                                <?php
                                    $exam_record = \App\Models\ExamRecord::where([
                                        'student_id' => $sr->user->id,
                                        'exam_id' => $exam->id,
                                        'year' => $year
                                    ])->first();
                                    $exam_average = $exam_record ? ($exam_record->ave ?? 0) : 0;

                                    // Si pas de moyenne en DB, calculer manuellement
                                    if($exam_average <= 0) {
                                        $marks = \App\Models\Mark::where([
                                            'student_id' => $sr->user->id,
                                            'exam_id' => $exam->id,
                                            'my_class_id' => $my_class->id,
                                            'year' => $year
                                        ])->get();

                                        $totalPoints = 0;
                                        $totalCoef = 0;

                                        foreach($marks as $mark) {
                                            $subject = \App\Models\Subject::find($mark->subject_id);
                                            if($subject) {
                                                $termValue = 0;
                                                if($exam->term == 1) $termValue = $mark->tex1;
                                                elseif($exam->term == 2) $termValue = $mark->tex2;
                                                elseif($exam->term == 3) $termValue = $mark->tex3;

                                                if($termValue > 0) {
                                                    $totalPoints += $termValue;
                                                    $totalCoef += $subject->coef;
                                                }
                                            }
                                        }

                                        $exam_average = $totalCoef > 0 ? $totalPoints / $totalCoef : 0;
                                    }
                                ?>
                                <?php if($exam_average > 0): ?>
                                    $("#moyenne_exam_<?php echo e($exam->id); ?>").text("<?php echo e(number_format($exam_average, 2)); ?>/20");
                                <?php else: ?>
                                    $("#moyenne_exam_<?php echo e($exam->id); ?>").text("0.00/20");
                                <?php endif; ?>
                            }
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                }

                // S'assurer que tous les examens affichent leurs moyennes
                ensureAllExamAveragesDisplayed();

                // Calculer immédiatement après avoir mis à jour la moyenne actuelle
                setTimeout(function() {
                    calculateAnnualAverage();
                }, 100);

                // Recalculer après un délai plus long pour s'assurer que tous les éléments sont mis à jour
                setTimeout(function() {
                    ensureAllExamAveragesDisplayed();
                    calculateAnnualAverage();

                    // Test final de cohérence
                    setTimeout(function() {
                        console.log("=== TEST FINAL DE COHÉRENCE (SHOW) ===");
                        <?php $__currentLoopData = $all_exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            var examAvg = $("#moyenne_exam_<?php echo e($exam->id); ?>").text();
                            console.log("<?php echo e($exam->name); ?>: " + examAvg);
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        var finalAvg = $("#moyenne_annuelle_finale").text();
                        console.log("Moyenne annuelle finale: " + finalAvg);
                        console.log("=== FIN TEST (SHOW) ===");
                    }, 500);
                }, 1500);
            <?php endif; ?>
        }, 500); // Attendre 500ms pour s'assurer que la moyenne est calculée
    });
</script>


<?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/marks/show/sheet.blade.php ENDPATH**/ ?>